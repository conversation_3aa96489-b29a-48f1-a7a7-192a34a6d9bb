import type { ClientReadableStream } from '@grpc/grpc-js'
import { Emitter } from '@kdt310722/utils/event'
import { type Awaitable, createDeferred } from '@kdt310722/utils/promise'

export enum StreamState {
    CLOSED,
    SUBSCRIBING,
}

export interface StreamWrapperTimeoutOptions {
    close?: number
}

export interface StreamWrapperOptions {
    timeout?: StreamWrapperTimeoutOptions
}

export type StreamWrapperEvents = {}

export class StreamWrapper<TResponse, TStream extends ClientReadableStream<TResponse> = ClientReadableStream<TResponse>> extends Emitter<StreamWrapperEvents> {
    protected readonly closeTimeout: number

    protected stream?: TStream

    #state: StreamState = StreamState.CLOSED

    public constructor(protected readonly subscriber: () => Awaitable<TStream>, { timeout = {} }: StreamWrapperOptions = {}) {
        super()

        this.closeTimeout = timeout.close ?? 10_000
    }

    public get state() {
        return this.#state
    }

    public async subscribe() {}

    public async close() {}

    protected async createStream() {
        this.setState(StreamState.SUBSCRIBING)

        const promise = createDeferred<TStream>()

        try {
            this.stream = await this.subscriber()
        } catch (error) {
            promise.reject(error)
        }
    }

    protected setState(state: StreamState) {
        this.#state = state
    }
}
